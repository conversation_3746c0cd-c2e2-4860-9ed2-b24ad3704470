package com.dangbei.devbase.client.dto.cmd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 腾讯云文档审核提交命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TencentDocumentReviewSubmitCmd extends BaseCmd {

    @Schema(description = "文档文件的链接地址，例如 http://www.example.com/doctest.doc")
    @NotBlank(message = "文档URL不能为空")
    private String url;

    @Schema(description = "指定文档文件的类型，如未指定则默认以文件的后缀为类型。例如：doc、docx、ppt、pptx等")
    private String type;

    @Schema(description = "审核的场景类型，有效值：Porn（涉黄）、Ads（广告），可以传入多种类型，不同类型以逗号分隔，例如：Porn,Ads")
    private String detectType;

    @Schema(description = "审核策略，不填写则使用默认策略。可在控制台进行配置")
    private String bizType;

    @Schema(description = "审核结果回调地址，支持以 http:// 或者 https:// 开头的地址")
    private String callback;

    @Schema(description = "存储桶名称，格式为 BucketName-APPID")
    private String bucket;

    @Schema(description = "存储桶所在地域")
    private String region;
}
