package com.dangbei.devbase.client.dto;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 腾讯云文档审核提交响应对象
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TencentDocumentReviewSubmitResp extends DTO {

    @Schema(description = "文档审核任务的ID")
    private String jobId;

    @Schema(description = "文档审核任务的状态，值为 Submitted（已提交审核）、Success（审核成功）、Failed（审核失败）、Auditing（审核中）其中一个")
    private String state;

    @Schema(description = "文档审核任务的创建时间")
    private String creationTime;

    @Schema(description = "腾讯云唯一请求ID")
    private String requestId;
}
