package com.dangbei.devbase.client.dto.cmd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 腾讯云文档审核查询命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TencentDocumentReviewQueryCmd extends BaseCmd {

    @Schema(description = "文档审核任务的ID")
    @NotBlank(message = "任务ID不能为空")
    private String jobId;

    @Schema(description = "存储桶名称，格式为 BucketName-APPID")
    @NotBlank(message = "存储桶名称不能为空")
    private String bucket;

    @Schema(description = "存储桶所在地域")
    @NotBlank(message = "存储桶地域不能为空")
    private String region;
}
