package com.dangbei.devbase.client.dto;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Objects;

/**
 * 腾讯云文档审核查询响应对象
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TencentDocumentReviewQueryResp extends DTO {

    @Schema(description = "文档审核任务的ID")
    private String jobId;

    @Schema(description = "文档审核任务的状态，值为 Submitted（已提交审核）、Success（审核成功）、Failed（审核失败）、Auditing（审核中）其中一个")
    private String state;

    @Schema(description = "文档审核任务的创建时间")
    private String creationTime;

    @Schema(description = "审核结果，0（审核正常），1 （判定为违规敏感文件），2（疑似敏感，建议人工复核）")
    private Integer result;

    @Schema(description = "审核结果详情")
    private String label;

    @Schema(description = "该字段表示审核结果的分类，取值为：Normal、Porn、Ads等")
    private String category;

    @Schema(description = "该字段表示审核结果的子分类")
    private String subLabel;

    @Schema(description = "机器审核的分数，分数越高表示越敏感")
    private Integer score;

    @Schema(description = "审核的文本内容")
    private String text;

    @Schema(description = "审核页面的详细结果")
    private List<PageResult> pageSegment;

    @Schema(description = "腾讯云唯一请求ID")
    private String requestId;

    /**
     * 页面审核结果
     */
    @Data
    public static class PageResult {
        @Schema(description = "页面URL")
        private String url;

        @Schema(description = "页面文本内容")
        private String text;

        @Schema(description = "页面审核结果，0（审核正常），1 （判定为违规敏感文件），2（疑似敏感，建议人工复核）")
        private Integer result;

        @Schema(description = "审核结果详情")
        private String label;

        @Schema(description = "该字段表示审核结果的分类")
        private String category;

        @Schema(description = "该字段表示审核结果的子分类")
        private String subLabel;

        @Schema(description = "机器审核的分数")
        private Integer score;
    }

    @Schema(description = "审核是否通过")
    public boolean isReviewPassed() {
        return Objects.equals(result, 0);
    }

    @Schema(description = "是否为违规内容")
    public boolean isViolation() {
        return Objects.equals(result, 1);
    }

    @Schema(description = "是否需要人工复核")
    public boolean needsManualReview() {
        return Objects.equals(result, 2);
    }

    @Schema(description = "审核是否完成")
    public boolean isCompleted() {
        return "Success".equals(state) || "Failed".equals(state);
    }
}
