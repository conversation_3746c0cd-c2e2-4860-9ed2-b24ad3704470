package com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.devbase.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ShortLink DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("short_link")
public class ShortLinkDO extends BaseDO<Long> {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "租户key")
    @TableField(value = "tenant_key")
    private String tenantKey;

    @Schema(description = "长链")
    @TableField(value = "url")
    private String url;

    @Schema(description = "短链key")
    @TableField(value = "short_key")
    private String shortKey;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
