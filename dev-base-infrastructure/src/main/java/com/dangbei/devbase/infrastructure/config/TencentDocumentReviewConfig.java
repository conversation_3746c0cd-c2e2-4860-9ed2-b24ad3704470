package com.dangbei.devbase.infrastructure.config;

import com.dangbei.devbase.infrastructure.config.properties.TencentDocumentReviewConfigProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * 腾讯云文档审核配置
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@Configuration
public class TencentDocumentReviewConfig {

    @Resource
    private TencentDocumentReviewConfigProperties properties;

    @Bean("tencentDocumentReviewRestTemplate")
    public RestTemplate getTencentDocumentReviewRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(properties.getConnectTimeout());
        factory.setReadTimeout(properties.getReadTimeout());
        return new RestTemplate(factory);
    }
}
