package com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.dangbei.devbase.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DingTalkLog DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ding_talk_log", autoResultMap = true)
public class DingTalkLogDO extends BaseDO<Long> {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "租户key")
    @TableField(value = "tenant_key")
    private String tenantKey;

    @Schema(description = "钉钉群名称")
    @TableField(value = "group_name")
    private String groupName;

    @Schema(description = "场景值编码")
    @TableField(value = "scene_code")
    private String sceneCode;

    @Schema(description = "标题")
    @TableField(value = "title")
    private String title;

    @Schema(description = "内容")
    @TableField(value = "content")
    private String content;

    @Schema(description = "艾特成员手机号列表")
    @TableField(value = "at_mobiles", typeHandler = FastjsonTypeHandler.class)
    private List<String> atMobiles;

    @Schema(description = "是否艾特所有人(1:是;0:否)")
    @TableField(value = "at_all")
    private Integer atAll;

    @Schema(description = "发送时间")
    @TableField(value = "send_time")
    private LocalDateTime sendTime;

    @Schema(description = "发送状态(1:成功;0:失败)")
    @TableField(value = "send_status")
    private Integer sendStatus;

    @Schema(description = "响应内容")
    @TableField(value = "response_content")
    private String responseContent;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
