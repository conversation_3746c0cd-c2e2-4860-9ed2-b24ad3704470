package com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.devbase.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TencentCaptchaConfig DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tencent_captcha_config")
public class TencentCaptchaConfigDO extends BaseDO<Long> {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "租户key")
    @TableField(value = "tenant_key")
    private String tenantKey;

    @Schema(description = "应用ID")
    @TableField(value = "captcha_app_id")
    private String captchaAppId;

    @Schema(description = "应用密钥")
    @TableField(value = "app_secret_key")
    private String appSecretKey;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
