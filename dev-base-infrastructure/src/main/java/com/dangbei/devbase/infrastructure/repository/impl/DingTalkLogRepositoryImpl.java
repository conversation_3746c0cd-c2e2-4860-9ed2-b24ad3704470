package com.dangbei.devbase.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject.DingTalkLogDO;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.mapper.DingTalkLogMapper;
import com.dangbei.devbase.infrastructure.repository.DingTalkLogRepository;
import org.springframework.stereotype.Repository;

/**
 * DingTalkLog 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Repository
public class DingTalkLogRepositoryImpl extends ServiceImpl<DingTalkLogMapper, DingTalkLogDO> implements DingTalkLogRepository {

}
