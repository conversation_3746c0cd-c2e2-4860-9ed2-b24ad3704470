package com.dangbei.devbase.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject.TencentCaptchaConfigDO;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.mapper.TencentCaptchaConfigMapper;
import com.dangbei.devbase.infrastructure.repository.TencentCaptchaConfigRepository;
import org.springframework.stereotype.Repository;

/**
 * TencentCaptchaConfig 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-23
 */
@Repository
public class TencentCaptchaConfigRepositoryImpl extends ServiceImpl<TencentCaptchaConfigMapper, TencentCaptchaConfigDO> implements TencentCaptchaConfigRepository {

}
