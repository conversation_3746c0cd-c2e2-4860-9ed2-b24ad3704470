package com.dangbei.devbase.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.devbase.domain.entity.TenantEntity;
import com.dangbei.devbase.domain.gateway.TenantGateway;
import com.dangbei.devbase.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.devbase.infrastructure.convertor.TenantConvertor;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject.TenantDO;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.mapper.TenantMapper;
import org.springframework.stereotype.Component;

/**
 * Tenant 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-11-11
 */
@Component
public class TenantGatewayImpl extends BaseGatewayImpl<Long, TenantEntity, <PERSON>antD<PERSON>, TenantMapper, TenantConvertor> implements TenantGateway {

    @Override
    public TenantEntity getByTenantKey(String tenantKey) {
        LambdaQueryWrapper<TenantDO> queryWrapper = Wrappers.lambdaQuery(TenantDO.class).eq(TenantDO::getTenantKey, tenantKey);
        return convertor.toEntity(baseMapper.selectOne(queryWrapper));
    }
}
