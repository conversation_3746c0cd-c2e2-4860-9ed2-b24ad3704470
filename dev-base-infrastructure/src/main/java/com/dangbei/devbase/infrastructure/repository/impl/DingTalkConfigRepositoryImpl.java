package com.dangbei.devbase.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject.DingTalkConfigDO;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.mapper.DingTalkConfigMapper;
import com.dangbei.devbase.infrastructure.repository.DingTalkConfigRepository;
import org.springframework.stereotype.Repository;

/**
 * DingTalkConfig 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-11-18
 */
@Repository
public class DingTalkConfigRepositoryImpl extends ServiceImpl<DingTalkConfigMapper, DingTalkConfigDO> implements DingTalkConfigRepository {

}
