package com.dangbei.devbase.infrastructure.gatewayimpl;

import com.dangbei.devbase.domain.entity.SmsLogEntity;
import com.dangbei.devbase.domain.gateway.SmsLogGateway;
import com.dangbei.devbase.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.devbase.infrastructure.convertor.SmsLogConvertor;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject.SmsLogDO;
import com.dangbei.devbase.infrastructure.gatewayimpl.database.mapper.SmsLogMapper;
import org.springframework.stereotype.Component;

/**
 * SmsLog 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-23
 */
@Component
public class SmsLogGatewayImpl extends BaseGatewayImpl<Long, SmsLogEntity, SmsLogDO, SmsLogMapper, SmsLogConvertor> implements SmsLogGateway {

}
