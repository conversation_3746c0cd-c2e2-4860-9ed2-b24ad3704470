package com.dangbei.devbase.infrastructure.remote.client;

import com.alibaba.fastjson2.JSON;
import com.dangbei.devbase.infrastructure.config.properties.TencentDocumentReviewConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 腾讯云文档审核客户端
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@Slf4j
@Component
public class TencentDocumentReviewClient {

    @Resource
    private TencentDocumentReviewConfigProperties properties;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 提交文档审核任务
     * 参考文档：<a href="https://cloud.tencent.com/document/product/1532/64604">腾讯云文档审核 API</a>
     * @param bucket 存储桶名称
     * @param region 存储桶地域
     * @param url 文档URL
     * @param type 文档类型
     * @param detectType 审核场景类型
     * @param bizType 审核策略
     * @param callback 回调地址
     * @return 提交结果
     */
    public String submitDocumentReview(String bucket, String region, String url, String type, 
                                     String detectType, String bizType, String callback) {
        try {
            String host = bucket + ".ci." + region + ".myqcloud.com";
            String path = "/document/auditing";
            String method = "POST";
            
            // 构建请求体
            Map<String, Object> request = new HashMap<>();
            Map<String, Object> input = new HashMap<>();
            input.put("Url", url);
            if (StringUtils.isNotBlank(type)) {
                input.put("Type", type);
            }
            request.put("Input", input);
            
            Map<String, Object> conf = new HashMap<>();
            if (StringUtils.isNotBlank(detectType)) {
                conf.put("DetectType", detectType);
            }
            if (StringUtils.isNotBlank(bizType)) {
                conf.put("BizType", bizType);
            }
            if (StringUtils.isNotBlank(callback)) {
                conf.put("Callback", callback);
            }
            request.put("Conf", conf);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("Request", request);
            
            String body = convertToXml(requestBody);
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_XML);
            headers.set("Host", host);
            
            String date = getGMTDate();
            headers.set("Date", date);
            
            // 生成签名
            String authorization = generateAuthorization(method, path, headers, body, date);
            headers.set("Authorization", authorization);
            
            HttpEntity<String> entity = new HttpEntity<>(body, headers);
            String requestUrl = "https://" + host + path;
            
            log.debug("[腾讯云文档审核]提交任务请求: url={}, body={}", requestUrl, body);
            
            ResponseEntity<String> response = restTemplate.exchange(requestUrl, HttpMethod.POST, entity, String.class);
            
            log.debug("[腾讯云文档审核]提交任务响应: {}", response.getBody());
            
            return response.getBody();
        } catch (Exception e) {
            log.error("[腾讯云文档审核]提交任务异常: url={}", url, e);
            throw new RuntimeException("提交文档审核任务失败", e);
        }
    }

    /**
     * 查询文档审核任务结果
     * @param bucket 存储桶名称
     * @param region 存储桶地域
     * @param jobId 任务ID
     * @return 查询结果
     */
    public String queryDocumentReview(String bucket, String region, String jobId) {
        try {
            String host = bucket + ".ci." + region + ".myqcloud.com";
            String path = "/document/auditing/" + jobId;
            String method = "GET";
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Host", host);
            
            String date = getGMTDate();
            headers.set("Date", date);
            
            // 生成签名
            String authorization = generateAuthorization(method, path, headers, "", date);
            headers.set("Authorization", authorization);
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            String requestUrl = "https://" + host + path;
            
            log.debug("[腾讯云文档审核]查询任务请求: url={}", requestUrl);
            
            ResponseEntity<String> response = restTemplate.exchange(requestUrl, HttpMethod.GET, entity, String.class);
            
            log.debug("[腾讯云文档审核]查询任务响应: {}", response.getBody());
            
            return response.getBody();
        } catch (Exception e) {
            log.error("[腾讯云文档审核]查询任务异常: jobId={}", jobId, e);
            throw new RuntimeException("查询文档审核任务失败", e);
        }
    }

    /**
     * 将Map转换为XML格式
     */
    private String convertToXml(Map<String, Object> map) {
        StringBuilder xml = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            xml.append("<").append(entry.getKey()).append(">");
            if (entry.getValue() instanceof Map) {
                xml.append(convertToXml((Map<String, Object>) entry.getValue()));
            } else {
                xml.append(entry.getValue());
            }
            xml.append("</").append(entry.getKey()).append(">");
        }
        return xml.toString();
    }

    /**
     * 获取GMT格式的日期
     */
    private String getGMTDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        return sdf.format(new Date());
    }

    /**
     * 生成腾讯云签名
     */
    private String generateAuthorization(String method, String path, HttpHeaders headers, String body, String date) {
        try {
            // 这里简化处理，实际项目中需要按照腾讯云签名算法实现
            // 参考：https://cloud.tencent.com/document/product/1532/64603
            String secretId = properties.getSecretId();
            String secretKey = properties.getSecretKey();
            
            long timestamp = System.currentTimeMillis() / 1000;
            String keyTime = timestamp + ";" + (timestamp + 3600);
            
            // 构建签名字符串（简化版本）
            String signKey = hmacSha1(keyTime, secretKey);
            String stringToSign = method.toLowerCase() + "\n" + path + "\n\n\n";
            String signature = hmacSha1(stringToSign, signKey);
            
            return String.format("q-sign-algorithm=sha1&q-ak=%s&q-sign-time=%s&q-key-time=%s&q-header-list=&q-url-param-list=&q-signature=%s",
                    secretId, keyTime, keyTime, signature);
        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * HMAC-SHA1加密
     */
    private String hmacSha1(String data, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        mac.init(secretKeySpec);
        byte[] result = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(result);
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}
