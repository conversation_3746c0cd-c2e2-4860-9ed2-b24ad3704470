package com.dangbei.devbase.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 腾讯云文档审核配置属性
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@Data
@Component
@NacosConfigurationProperties(
    prefix = "tencent.document.review",
    groupId = "${nacos.config.group}",
    dataId = "${nacos.config.data-id}",
    autoRefreshed = true)
public class TencentDocumentReviewConfigProperties {

    @Schema(description = "SecretId")
    private String secretId = "AKID7KbyCSm9NPI2avKh0774yb2KZieT7Yqw";

    @Schema(description = "SecretKey")
    private String secretKey = "znPp3BSdeR9pRs6CPYWK3hAXpywhKxQg";

    @Schema(description = "默认存储桶名称，格式为 BucketName-APPID")
    private String defaultBucket = "example-bucket-**********";

    @Schema(description = "默认存储桶地域")
    private String defaultRegion = "ap-beijing";

    @Schema(description = "默认审核场景类型")
    private String defaultDetectType = "Porn,Ads";

    @Schema(description = "默认审核策略")
    private String defaultBizType = "";

    @Schema(description = "连接超时时间（毫秒）")
    private Integer connectTimeout = 5000;

    @Schema(description = "读取超时时间（毫秒）")
    private Integer readTimeout = 10000;
}
