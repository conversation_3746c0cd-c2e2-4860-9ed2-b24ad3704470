package com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.devbase.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DingTalkConfig DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-11-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ding_talk_config")
public class DingTalkConfigDO extends BaseDO<Long> {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "租户key")
    @TableField(value = "tenant_key")
    private String tenantKey;

    @Schema(description = "场景值编码")
    @TableField(value = "scene_code")
    private String sceneCode;

    @Schema(description = "场景值描述")
    @TableField(value = "scene_desc")
    private String sceneDesc;

    @Schema(description = "钉钉机器人webhook地址")
    @TableField(value = "webhook_url")
    private String webhookUrl;

    @Schema(description = "钉钉群名称")
    @TableField(value = "group_name")
    private String groupName;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
