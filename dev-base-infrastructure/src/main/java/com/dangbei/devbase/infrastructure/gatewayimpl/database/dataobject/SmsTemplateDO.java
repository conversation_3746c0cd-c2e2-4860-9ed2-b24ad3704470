package com.dangbei.devbase.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.devbase.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SmsTemplate DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sms_template")
public class SmsTemplateDO extends BaseDO<Long> {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "租户key")
    @TableField(value = "tenant_key")
    private String tenantKey;

    @Schema(description = "账号编码")
    @TableField(value = "account_code")
    private String accountCode;

    @Schema(description = "模板编码")
    @TableField(value = "template_code")
    private String templateCode;

    @Schema(description = "模板名称")
    @TableField(value = "template_name")
    private String templateName;

    @Schema(description = "模版内容")
    @TableField(value = "template_content")
    private String templateContent;

    @Schema(description = "签名")
    @TableField(value = "sign_name")
    private String signName;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
