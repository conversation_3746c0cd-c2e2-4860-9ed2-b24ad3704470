package com.dangbei.devbase.app.executor;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.dangbei.devbase.client.dto.TencentDocumentReviewQueryResp;
import com.dangbei.devbase.client.dto.cmd.TencentDocumentReviewQueryCmd;
import com.dangbei.devbase.infrastructure.remote.client.TencentDocumentReviewClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 腾讯云文档审核查询命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@Slf4j
@Component
public class TencentDocumentReviewQueryCmdExe {

    @Resource
    private TencentDocumentReviewClient tencentDocumentReviewClient;

    /**
     * 执行腾讯云文档审核查询
     * @param queryCmd 查询命令
     * @return 包含审核结果的详细信息
     */
    public SingleResponse<TencentDocumentReviewQueryResp> execute(TencentDocumentReviewQueryCmd queryCmd) {
        log.debug("[腾讯云文档审核查询]开始,queryCmd={}", JSON.toJSONString(queryCmd));

        try {
            // 调用腾讯云文档审核查询API
            String responseXml = tencentDocumentReviewClient.queryDocumentReview(
                queryCmd.getBucket(), queryCmd.getRegion(), queryCmd.getJobId());

            // 解析XML响应
            TencentDocumentReviewQueryResp queryResp = parseQueryResponse(responseXml);
            
            log.debug("[腾讯云文档审核查询]完成,queryResp={}", JSON.toJSONString(queryResp));
            
            return SingleResponse.of(queryResp);
        } catch (Exception e) {
            log.error("[腾讯云文档审核查询]异常,queryCmd={}", JSON.toJSONString(queryCmd), e);
            return SingleResponse.buildFailure("QUERY_FAILED", "查询文档审核任务失败: " + e.getMessage());
        }
    }

    /**
     * 解析查询响应XML
     */
    private TencentDocumentReviewQueryResp parseQueryResponse(String responseXml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(responseXml.getBytes()));

            TencentDocumentReviewQueryResp resp = new TencentDocumentReviewQueryResp();

            // 解析JobsDetail节点
            NodeList jobsDetailList = document.getElementsByTagName("JobsDetail");
            if (jobsDetailList.getLength() > 0) {
                Element jobsDetail = (Element) jobsDetailList.item(0);
                
                resp.setJobId(getElementText(jobsDetail, "JobId"));
                resp.setState(getElementText(jobsDetail, "State"));
                resp.setCreationTime(getElementText(jobsDetail, "CreationTime"));

                // 解析审核结果
                NodeList resultList = jobsDetail.getElementsByTagName("Result");
                if (resultList.getLength() > 0) {
                    try {
                        resp.setResult(Integer.valueOf(resultList.item(0).getTextContent()));
                    } catch (NumberFormatException e) {
                        log.warn("解析Result失败: {}", resultList.item(0).getTextContent());
                    }
                }

                resp.setLabel(getElementText(jobsDetail, "Label"));
                resp.setCategory(getElementText(jobsDetail, "Category"));
                resp.setSubLabel(getElementText(jobsDetail, "SubLabel"));
                
                NodeList scoreList = jobsDetail.getElementsByTagName("Score");
                if (scoreList.getLength() > 0) {
                    try {
                        resp.setScore(Integer.valueOf(scoreList.item(0).getTextContent()));
                    } catch (NumberFormatException e) {
                        log.warn("解析Score失败: {}", scoreList.item(0).getTextContent());
                    }
                }

                resp.setText(getElementText(jobsDetail, "Text"));

                // 解析页面审核结果
                NodeList pageSegmentList = jobsDetail.getElementsByTagName("PageSegment");
                if (pageSegmentList.getLength() > 0) {
                    List<TencentDocumentReviewQueryResp.PageResult> pageResults = new ArrayList<>();
                    for (int i = 0; i < pageSegmentList.getLength(); i++) {
                        Element pageElement = (Element) pageSegmentList.item(i);
                        TencentDocumentReviewQueryResp.PageResult pageResult = parsePageResult(pageElement);
                        if (pageResult != null) {
                            pageResults.add(pageResult);
                        }
                    }
                    resp.setPageSegment(pageResults);
                }
            }

            // 解析RequestId
            resp.setRequestId(getElementText(document.getDocumentElement(), "RequestId"));

            return resp;
        } catch (Exception e) {
            log.error("解析查询响应XML失败: {}", responseXml, e);
            throw new RuntimeException("解析响应失败", e);
        }
    }

    /**
     * 解析页面审核结果
     */
    private TencentDocumentReviewQueryResp.PageResult parsePageResult(Element pageElement) {
        try {
            TencentDocumentReviewQueryResp.PageResult pageResult = new TencentDocumentReviewQueryResp.PageResult();
            
            pageResult.setUrl(getElementText(pageElement, "Url"));
            pageResult.setText(getElementText(pageElement, "Text"));
            pageResult.setLabel(getElementText(pageElement, "Label"));
            pageResult.setCategory(getElementText(pageElement, "Category"));
            pageResult.setSubLabel(getElementText(pageElement, "SubLabel"));
            
            String resultText = getElementText(pageElement, "Result");
            if (resultText != null) {
                try {
                    pageResult.setResult(Integer.valueOf(resultText));
                } catch (NumberFormatException e) {
                    log.warn("解析页面Result失败: {}", resultText);
                }
            }
            
            String scoreText = getElementText(pageElement, "Score");
            if (scoreText != null) {
                try {
                    pageResult.setScore(Integer.valueOf(scoreText));
                } catch (NumberFormatException e) {
                    log.warn("解析页面Score失败: {}", scoreText);
                }
            }
            
            return pageResult;
        } catch (Exception e) {
            log.warn("解析页面审核结果失败", e);
            return null;
        }
    }

    /**
     * 获取元素文本内容
     */
    private String getElementText(Element parent, String tagName) {
        NodeList nodeList = parent.getElementsByTagName(tagName);
        if (nodeList.getLength() > 0) {
            return nodeList.item(0).getTextContent();
        }
        return null;
    }
}
