package com.dangbei.devbase.app.executor;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.dangbei.devbase.client.dto.TencentDocumentReviewSubmitResp;
import com.dangbei.devbase.client.dto.cmd.TencentDocumentReviewSubmitCmd;
import com.dangbei.devbase.infrastructure.config.properties.TencentDocumentReviewConfigProperties;
import com.dangbei.devbase.infrastructure.remote.client.TencentDocumentReviewClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;

/**
 * 腾讯云文档审核提交命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@Slf4j
@Component
public class TencentDocumentReviewSubmitCmdExe {

    @Resource
    private TencentDocumentReviewClient tencentDocumentReviewClient;

    @Resource
    private TencentDocumentReviewConfigProperties properties;

    /**
     * 执行腾讯云文档审核提交
     * @param submitCmd 提交命令
     * @return 包含任务ID和状态的提交结果
     */
    public SingleResponse<TencentDocumentReviewSubmitResp> execute(TencentDocumentReviewSubmitCmd submitCmd) {
        log.debug("[腾讯云文档审核提交]开始,submitCmd={}", JSON.toJSONString(submitCmd));

        try {
            // 使用默认值填充空字段
            String bucket = StringUtils.isNotBlank(submitCmd.getBucket()) ? 
                submitCmd.getBucket() : properties.getDefaultBucket();
            String region = StringUtils.isNotBlank(submitCmd.getRegion()) ? 
                submitCmd.getRegion() : properties.getDefaultRegion();
            String detectType = StringUtils.isNotBlank(submitCmd.getDetectType()) ? 
                submitCmd.getDetectType() : properties.getDefaultDetectType();
            String bizType = StringUtils.isNotBlank(submitCmd.getBizType()) ? 
                submitCmd.getBizType() : properties.getDefaultBizType();

            // 调用腾讯云文档审核API
            String responseXml = tencentDocumentReviewClient.submitDocumentReview(
                bucket, region, submitCmd.getUrl(), submitCmd.getType(), 
                detectType, bizType, submitCmd.getCallback());

            // 解析XML响应
            TencentDocumentReviewSubmitResp submitResp = parseSubmitResponse(responseXml);
            
            log.debug("[腾讯云文档审核提交]完成,submitResp={}", JSON.toJSONString(submitResp));
            
            return SingleResponse.of(submitResp);
        } catch (Exception e) {
            log.error("[腾讯云文档审核提交]异常,submitCmd={}", JSON.toJSONString(submitCmd), e);
            return SingleResponse.buildFailure("SUBMIT_FAILED", "提交文档审核任务失败: " + e.getMessage());
        }
    }

    /**
     * 解析提交响应XML
     */
    private TencentDocumentReviewSubmitResp parseSubmitResponse(String responseXml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(responseXml.getBytes()));

            TencentDocumentReviewSubmitResp resp = new TencentDocumentReviewSubmitResp();

            // 解析JobsDetail节点
            NodeList jobsDetailList = document.getElementsByTagName("JobsDetail");
            if (jobsDetailList.getLength() > 0) {
                Element jobsDetail = (Element) jobsDetailList.item(0);
                
                NodeList jobIdList = jobsDetail.getElementsByTagName("JobId");
                if (jobIdList.getLength() > 0) {
                    resp.setJobId(jobIdList.item(0).getTextContent());
                }
                
                NodeList stateList = jobsDetail.getElementsByTagName("State");
                if (stateList.getLength() > 0) {
                    resp.setState(stateList.item(0).getTextContent());
                }
                
                NodeList creationTimeList = jobsDetail.getElementsByTagName("CreationTime");
                if (creationTimeList.getLength() > 0) {
                    resp.setCreationTime(creationTimeList.item(0).getTextContent());
                }
            }

            // 解析RequestId（如果存在）
            NodeList requestIdList = document.getElementsByTagName("RequestId");
            if (requestIdList.getLength() > 0) {
                resp.setRequestId(requestIdList.item(0).getTextContent());
            }

            return resp;
        } catch (Exception e) {
            log.error("解析提交响应XML失败: {}", responseXml, e);
            throw new RuntimeException("解析响应失败", e);
        }
    }
}
