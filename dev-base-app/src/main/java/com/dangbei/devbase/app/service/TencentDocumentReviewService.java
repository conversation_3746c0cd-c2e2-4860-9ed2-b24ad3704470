package com.dangbei.devbase.app.service;

import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.devbase.app.executor.TencentDocumentReviewQueryCmdExe;
import com.dangbei.devbase.app.executor.TencentDocumentReviewSubmitCmdExe;
import com.dangbei.devbase.client.dto.TencentDocumentReviewQueryResp;
import com.dangbei.devbase.client.dto.TencentDocumentReviewSubmitResp;
import com.dangbei.devbase.client.dto.cmd.TencentDocumentReviewQueryCmd;
import com.dangbei.devbase.client.dto.cmd.TencentDocumentReviewSubmitCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 腾讯云文档审核服务
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@Slf4j
@Service
public class TencentDocumentReviewService {

    @Resource
    private TencentDocumentReviewSubmitCmdExe tencentDocumentReviewSubmitCmdExe;
    
    @Resource
    private TencentDocumentReviewQueryCmdExe tencentDocumentReviewQueryCmdExe;

    /**
     * 提交文档审核任务
     * 提交一个文档审核任务，可审核您的文档文件是否存在敏感违规信息
     * 参考文档：<a href="https://cloud.tencent.com/document/product/1532/64604">腾讯云文档审核 API</a>
     * @param submitCmd 提交审核命令
     * @return {@link SingleResponse<TencentDocumentReviewSubmitResp>} 包含任务ID和状态的提交结果
     */
    public SingleResponse<TencentDocumentReviewSubmitResp> submitReview(TencentDocumentReviewSubmitCmd submitCmd) {
        return tencentDocumentReviewSubmitCmdExe.execute(submitCmd);
    }

    /**
     * 查询文档审核任务结果
     * 查询已提交的文档审核任务的详细结果
     * 参考文档：<a href="https://cloud.tencent.com/document/product/1532/64605">腾讯云文档审核查询 API</a>
     * @param queryCmd 查询命令
     * @return {@link SingleResponse<TencentDocumentReviewQueryResp>} 包含审核结果的详细信息
     */
    public SingleResponse<TencentDocumentReviewQueryResp> queryReview(TencentDocumentReviewQueryCmd queryCmd) {
        return tencentDocumentReviewQueryCmdExe.execute(queryCmd);
    }
}
