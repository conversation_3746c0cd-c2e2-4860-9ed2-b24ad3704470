package com.dangbei.devbase.app.facade;

import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.devbase.app.service.TencentDocumentReviewService;
import com.dangbei.devbase.client.dto.TencentDocumentReviewQueryResp;
import com.dangbei.devbase.client.dto.TencentDocumentReviewSubmitResp;
import com.dangbei.devbase.client.dto.cmd.TencentDocumentReviewQueryCmd;
import com.dangbei.devbase.client.dto.cmd.TencentDocumentReviewSubmitCmd;
import com.dangbei.devbase.client.facade.TencentDocumentReviewFacade;
import com.dangbei.devbase.common.constant.CommonConst;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 腾讯云文档审核服务接口实现
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-02
 */
@Service
@DubboService(group = CommonConst.Dubbo.GROUP)
public class TencentDocumentReviewFacadeImpl implements TencentDocumentReviewFacade {

    @Resource
    private TencentDocumentReviewService tencentDocumentReviewService;

    @Override
    public SingleResponse<TencentDocumentReviewSubmitResp> submitReview(TencentDocumentReviewSubmitCmd submitCmd) {
        return tencentDocumentReviewService.submitReview(submitCmd);
    }

    @Override
    public SingleResponse<TencentDocumentReviewQueryResp> queryReview(TencentDocumentReviewQueryCmd queryCmd) {
        return tencentDocumentReviewService.queryReview(queryCmd);
    }
}
